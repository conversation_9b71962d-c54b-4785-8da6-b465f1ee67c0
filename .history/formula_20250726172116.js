// ==UserScript==
// @name         test
// @namespace    http://tampermonkey.net/
// @version      1.7
// @description  自动公式转换工具(支持持久化)
// <AUTHOR>
// @match        https://www.notion.so/*
// @grant        GM_addStyle
// @require      https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js
// @downloadURL https://update.greasyfork.org/scripts/525730/Notion-Formula-Auto-Conversion-Tool.user.js
// @updateURL https://update.greasyfork.org/scripts/525730/Notion-Formula-Auto-Conversion-Tool.meta.js
// ==/UserScript==

(function() {
    'use strict';

    GM_addStyle(`
        /* 基础样式 */
        #formula-helper {
            position: fixed;
            bottom: 90px;
            right: 20px;
            z-index: 9999;
            background: white;
            padding: 0;
            border-radius: 12px;
            box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 30px,
                       rgba(0, 0, 0, 0.1) 0px 1px 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 200px;
            transform-origin: center;
            will-change: transform;
            overflow: hidden;
        }

        .content-wrapper {
            padding: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
        }

        /* 收起状态 */
        #formula-helper.collapsed {
            width: 48px;
            min-width: 48px;
            height: 48px;
            padding: 12px;
            opacity: 0.9;
            transform: scale(0.98);
            border-radius: 50%;
        }

        #formula-helper.collapsed .content-wrapper {
            opacity: 0;
            transform: scale(0.8);
            pointer-events: none;
            transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #formula-helper #convert-btn,
        #formula-helper #progress-container,
        #formula-helper #status-text {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 1;
            transform: translateY(0);
            transform-origin: center;
        }

        /* 收起按钮样式 */
        #collapse-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            border: none;
            background: transparent;
            cursor: pointer;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-origin: center;
            z-index: 2;
        }

        #collapse-btn:hover {
            transform: scale(1.1);
        }

        #collapse-btn:active {
            transform: scale(0.95);
        }

        #collapse-btn svg {
            width: 16px;
            height: 16px;
            fill: #4b5563;
            transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #formula-helper.collapsed #collapse-btn {
            position: static;
            width: 100%;
            height: 100%;
        }

        #formula-helper.collapsed #collapse-btn svg {
            transform: rotate(180deg);
        }

        @media (hover: hover) {
            #formula-helper:not(.collapsed):hover {
                transform: translateY(-2px);
                box-shadow: rgba(0, 0, 0, 0.15) 0px 15px 35px,
                           rgba(0, 0, 0, 0.12) 0px 3px 10px;
            }

            #formula-helper.collapsed:hover {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        /* 按钮样式 */
        #convert-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 20px;
            margin-bottom: 12px;
            width: 100%;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        #convert-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            opacity: 0;
            transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        #convert-btn:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        #convert-btn:hover::after {
            opacity: 1;
        }

        #convert-btn:active {
            transform: translateY(1px);
            box-shadow: 0 2px 6px rgba(37, 99, 235, 0.15);
        }

        #convert-btn.processing {
            background: #9ca3af;
            pointer-events: none;
            transform: scale(0.98);
            box-shadow: none;
        }

        /* 状态和进度显示 */
        #status-text {
            font-size: 13px;
            color: #4b5563;
            margin-bottom: 10px;
            line-height: 1.5;
        }

        #progress-container {
            background: #e5e7eb;
            height: 4px;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 15px;
            transform-origin: center;
        }

        #progress-bar {
            background: #2563eb;
            height: 100%;
            width: 0%;
            transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent
            );
            animation: progress-shine 1.5s linear infinite;
        }

        @keyframes progress-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 动画效果 */
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(0.98); }
            100% { opacity: 1; transform: scale(1); }
        }

        .processing #status-text {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
        }
    `);

    // 缓存DOM元素
    let panel, statusText, convertBtn, progressBar, progressContainer, collapseBtn;
    let isProcessing = false;
    let formulaCount = 0;
    let isCollapsed = true;
    let hoverTimer = null;

    function createPanel() {
        panel = document.createElement('div');
        panel.id = 'formula-helper';
        panel.classList.add('collapsed');
        panel.innerHTML = `
            <button id="collapse-btn">
                <svg viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
            <div class="content-wrapper">
                <button id="convert-btn">🔄 (0)</button>
                <div id="progress-container">
                    <div id="progress-bar"></div>
                </div>
                <div id="status-text">就绪</div>
            </div>
        `;
        document.body.appendChild(panel);

        statusText = panel.querySelector('#status-text');
        convertBtn = panel.querySelector('#convert-btn');
        progressBar = panel.querySelector('#progress-bar');
        progressContainer = panel.querySelector('#progress-container');
        collapseBtn = panel.querySelector('#collapse-btn');

        // 添加收起按钮事件
        collapseBtn.addEventListener('click', toggleCollapse);

        // 添加鼠标悬停事件
        panel.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimer);
            if (isCollapsed) {
                hoverTimer = setTimeout(() => {
                    panel.classList.remove('collapsed');
                    isCollapsed = false;
                }, 150); // 减少展开延迟时间
            }
        });

        panel.addEventListener('mouseleave', () => {
            clearTimeout(hoverTimer);
            if (!isCollapsed && !isProcessing) { // 添加处理中状态判断
                hoverTimer = setTimeout(() => {
                    panel.classList.add('collapsed');
                    isCollapsed = true;
                }, 800); // 适当减少收起延迟
            }
        });
    }

    function toggleCollapse() {
        isCollapsed = !isCollapsed;
        panel.classList.toggle('collapsed');
    }

    function updateProgress(current, total) {
        const percentage = total > 0 ? (current / total) * 100 : 0;
        progressBar.style.width = `${percentage}%`;
    }

    const sleep = ms => new Promise(resolve => setTimeout(resolve, ms));

    function updateStatus(text, timeout = 0) {
        statusText.textContent = text;
        if (timeout) {
            setTimeout(() => statusText.textContent = '就绪', timeout);
        }
        console.log('[状态]', text);
    }

    // 公式查找
    function findFormulas(text) {
        const formulas = [];

        // 首先查找跨行的$$...$$公式
        const multilineBlockRegex = /\$\$[\s\S]*?\$\$/g;
        let match;
        while ((match = multilineBlockRegex.exec(text)) !== null) {
            const formula = match[0];
            // 检查是否包含换行符（跨行公式）
            if (formula.includes('\n')) {
                formulas.push({
                    formula: formula,
                    index: match.index,
                    isMultiline: true
                });
            }
        }

        // 然后查找单行公式
        const combinedRegex = /\$\$(.*?)\$\$|\$([^\$\n]+?)\$|\\\((.*?)\\\)/gs;
        while ((match = combinedRegex.exec(text)) !== null) {
            const [fullMatch, blockFormula, inlineFormula, latexFormula] = match;
            const formula = fullMatch;

            if (formula && !formula.includes('\n')) {
                // 检查是否与已找到的跨行公式重叠
                const isOverlapping = formulas.some(f =>
                    f.isMultiline &&
                    match.index >= f.index &&
                    match.index < f.index + f.formula.length
                );

                if (!isOverlapping) {
                    formulas.push({
                        formula: fullMatch,
                        index: match.index,
                        isMultiline: false
                    });
                }
            }
        }

        // 按位置排序
        formulas.sort((a, b) => a.index - b.index);
        return formulas;
    }

    // 操作区域查找
    async function findOperationArea() {
        const selector = '.notion-overlay-container';
        for (let i = 0; i < 5; i++) {
            const areas = document.querySelectorAll(selector);
            const area = Array.from(areas).find(a =>
                a.style.display !== 'none' && a.querySelector('[role="button"]')
            );

            if (area) {
                console.log('找到操作区域');
                return area;
            }
            await sleep(50);
        }
        return null;
    }

    // 按钮查找
    async function findButton(area, options = {}) {
        const {
            buttonText = [],
            hasSvg = false,
            attempts = 8
        } = options;

        const buttons = area.querySelectorAll('[role="button"]');
        const cachedButtons = Array.from(buttons);

        for (let i = 0; i < attempts; i++) {
            const button = cachedButtons.find(btn => {
                if (hasSvg && btn.querySelector('svg.equation')) return true;
                const text = btn.textContent.toLowerCase();
                return buttonText.some(t => text.includes(t));
            });

            if (button) {
                return button;
            }
            await sleep(50);
        }
        return null;
    }

    // 处理跨行双美元符号公式
    async function handleMultilineFormula(editor, formula) {
        try {
            console.log('开始处理跨行公式:', formula);

            // 找到包含公式开始$$的文本节点
            const walker = document.createTreeWalker(editor, NodeFilter.SHOW_TEXT);
            const allTextNodes = [];
            let node;

            while (node = walker.nextNode()) {
                allTextNodes.push(node);
            }

            // 找到包含公式的所有文本节点
            let startNodeIndex = -1;
            let endNodeIndex = -1;
            let startOffset = -1;
            let endOffset = -1;

            // 查找开始的$$
            for (let i = 0; i < allTextNodes.length; i++) {
                const textContent = allTextNodes[i].textContent;
                const startIndex = textContent.indexOf('$$');
                if (startIndex !== -1) {
                    startNodeIndex = i;
                    startOffset = startIndex;
                    break;
                }
            }

            if (startNodeIndex === -1) {
                console.warn('未找到公式开始标记$$');
                return false;
            }

            // 查找结束的$$
            for (let i = startNodeIndex; i < allTextNodes.length; i++) {
                const textContent = allTextNodes[i].textContent;
                let searchStart = (i === startNodeIndex) ? startOffset + 2 : 0; // 跳过开始的$$
                const endIndex = textContent.indexOf('$$', searchStart);
                if (endIndex !== -1) {
                    endNodeIndex = i;
                    endOffset = endIndex + 2; // 包含结束的$$
                    break;
                }
            }

            if (endNodeIndex === -1) {
                console.warn('未找到公式结束标记$$');
                return false;
            }

            console.log(`找到跨行公式: 从节点${startNodeIndex}(${startOffset}) 到节点${endNodeIndex}(${endOffset})`);

            // 合并所有相关的文本内容
            let combinedText = '';
            for (let i = startNodeIndex; i <= endNodeIndex; i++) {
                const textContent = allTextNodes[i].textContent;
                if (i === startNodeIndex && i === endNodeIndex) {
                    // 开始和结束在同一个节点
                    combinedText = textContent.substring(startOffset, endOffset);
                } else if (i === startNodeIndex) {
                    // 开始节点
                    combinedText += textContent.substring(startOffset);
                } else if (i === endNodeIndex) {
                    // 结束节点
                    combinedText += textContent.substring(0, endOffset);
                } else {
                    // 中间节点
                    combinedText += textContent;
                }
            }

            // 将换行符替换为空格，创建单行版本
            const singleLineFormula = combinedText.replace(/\s+/g, ' ').trim();
            console.log('合并后的单行公式:', singleLineFormula);

            // 找到可编辑的父元素
            let editableElement = allTextNodes[startNodeIndex].parentElement;
            while (editableElement && editableElement.getAttribute('contenteditable') !== 'true' && editableElement !== document.body) {
                editableElement = editableElement.parentElement;
            }

            if (!editableElement || editableElement === document.body) {
                console.warn('无法找到可编辑的父元素');
                return false;
            }

            // 选中整个跨行公式区域
            const range = document.createRange();
            range.setStart(allTextNodes[startNodeIndex], startOffset);
            range.setEnd(allTextNodes[endNodeIndex], endOffset);

            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            editableElement.focus();
            await sleep(100);

            // 替换为单行版本
            document.execCommand('insertText', false, singleLineFormula);
            await sleep(200);

            // 现在对单行版本执行删除和重新输入$的逻辑
            return await handleSingleLineDoubleDollar(editableElement, singleLineFormula);

        } catch (error) {
            console.error('处理跨行公式时出错:', error);
            return false;
        }
    }

    // 处理单行双美元符号公式的删除和重新输入逻辑
    async function handleSingleLineDoubleDollar(editableElement, formula) {
        try {
            console.log('开始处理单行双美元符号:', formula);

            // 找到包含公式的文本节点
            const walker = document.createTreeWalker(editableElement, NodeFilter.SHOW_TEXT);
            let targetNode = null;
            let startOffset = -1;

            let node;
            while (node = walker.nextNode()) {
                const index = node.textContent.indexOf(formula);
                if (index !== -1) {
                    targetNode = node;
                    startOffset = index;
                    break;
                }
            }

            if (!targetNode || startOffset === -1) {
                console.warn('未找到目标文本节点');
                return false;
            }

            // 定位到最后一个$的位置
            const range = document.createRange();
            range.setStart(targetNode, startOffset + formula.length - 1);
            range.setEnd(targetNode, startOffset + formula.length);

            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            await sleep(50);

            // 删除最后一个$
            const beforeText = targetNode.textContent.substring(0, startOffset + formula.length - 1);
            const afterText = targetNode.textContent.substring(startOffset + formula.length);
            targetNode.textContent = beforeText + afterText;

            // 重新设置光标位置
            const newRange = document.createRange();
            newRange.setStart(targetNode, startOffset + formula.length - 1);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);

            await sleep(100);

            // 重新插入$
            const currentPos = startOffset + formula.length - 1;
            const newText = targetNode.textContent.substring(0, currentPos) + '$' + targetNode.textContent.substring(currentPos);
            targetNode.textContent = newText;

            // 设置光标到$后面
            const finalRange = document.createRange();
            finalRange.setStart(targetNode, currentPos + 1);
            finalRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(finalRange);

            // 触发输入事件
            const inputEvent = new InputEvent('input', {
                data: '$',
                inputType: 'insertText',
                bubbles: true,
                cancelable: true
            });
            editableElement.dispatchEvent(inputEvent);

            // 触发键盘事件
            const keyEvent = new KeyboardEvent('keydown', {
                key: '$',
                code: 'Digit4',
                keyCode: 52,
                which: 52,
                shiftKey: true,
                bubbles: true,
                cancelable: true
            });
            editableElement.dispatchEvent(keyEvent);

            await sleep(200);

            console.log('单行双美元符号处理完成');
            return true;

        } catch (error) {
            console.error('处理单行双美元符号时出错:', error);
            return false;
        }
    }

    // 优化的公式转换
    async function convertFormula(editor, formula, isMultiline = false) {
        try {
            // 如果是跨行公式，先处理合并逻辑
            if (isMultiline && formula.startsWith('$$') && formula.endsWith('$$')) {
                console.log('处理跨行双美元符号公式:', formula);
                return await handleMultilineFormula(editor, formula);
            }

            const walker = document.createTreeWalker(editor, NodeFilter.SHOW_TEXT);
            const textNodes = [];
            let node;

            while (node = walker.nextNode()) {
                if (node.textContent.includes(formula)) {
                    textNodes.unshift(node);
                }
            }

            if (!textNodes.length) {
                console.warn('未找到匹配的文本');
                return;
            }

            const targetNode = textNodes[0];
            const startOffset = targetNode.textContent.indexOf(formula);

            // 检查是否是连续两个$$的情况
            const isDoubleDollar = formula.startsWith('$$') && formula.endsWith('$$');

            if (isDoubleDollar) {
                // 对于 $$...$$，使用更直接的方法：直接修改文本内容然后触发输入事件
                console.log('处理双美元符号公式:', formula);

                // 找到可编辑的父元素
                let editableElement = targetNode.parentElement;
                while (editableElement && editableElement.getAttribute('contenteditable') !== 'true' && editableElement !== document.body) {
                    editableElement = editableElement.parentElement;
                }

                if (!editableElement || editableElement === document.body) {
                    console.warn('无法找到可编辑的父元素');
                    return false;
                }

                // 先将光标定位到最后一个$的位置
                const range = document.createRange();
                range.setStart(targetNode, startOffset + formula.length - 1);
                range.setEnd(targetNode, startOffset + formula.length);

                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);

                editableElement.focus();
                await sleep(50);

                // 直接修改文本内容，删除最后一个$
                const beforeText = targetNode.textContent.substring(0, startOffset + formula.length - 1);
                const afterText = targetNode.textContent.substring(startOffset + formula.length);
                targetNode.textContent = beforeText + afterText;

                // 重新设置光标位置到删除的$的位置
                const newRange = document.createRange();
                newRange.setStart(targetNode, startOffset + formula.length - 1);
                newRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(newRange);

                await sleep(100);

                // 触发输入事件来模拟用户输入$
                const inputEvent = new InputEvent('input', {
                    data: '$',
                    inputType: 'insertText',
                    bubbles: true,
                    cancelable: true
                });

                // 先插入$字符
                const currentPos = startOffset + formula.length - 1;
                const newText = targetNode.textContent.substring(0, currentPos) + '$' + targetNode.textContent.substring(currentPos);
                targetNode.textContent = newText;

                // 设置光标到$后面
                const finalRange = document.createRange();
                finalRange.setStart(targetNode, currentPos + 1);
                finalRange.collapse(true);
                selection.removeAllRanges();
                selection.addRange(finalRange);

                // 触发输入事件
                editableElement.dispatchEvent(inputEvent);

                // 也触发键盘事件
                const keyEvent = new KeyboardEvent('keydown', {
                    key: '$',
                    code: 'Digit4',
                    keyCode: 52,
                    which: 52,
                    shiftKey: true,
                    bubbles: true,
                    cancelable: true
                });
                editableElement.dispatchEvent(keyEvent);

                await sleep(200);

                console.log('双美元符号处理完成');
                return true;
            } else {
                // 原有逻辑：选中整个公式进行转换
                const range = document.createRange();
                range.setStart(targetNode, startOffset);
                range.setEnd(targetNode, startOffset + formula.length);

                const selection = window.getSelection();
                selection.removeAllRanges();
                selection.addRange(range);

                targetNode.parentElement.focus();
                document.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
                await sleep(50);

                const area = await findOperationArea();
                if (!area) throw new Error('未找到操作区域');

                const formulaButton = await findButton(area, {
                    hasSvg: true,
                    buttonText: ['equation', '公式', 'math']
                });
                if (!formulaButton) throw new Error('未找到公式按钮');

                await simulateClick(formulaButton);
                await sleep(50);

                const doneButton = await findButton(document, {
                    buttonText: ['done', '完成'],
                    attempts: 10
                });
                if (!doneButton) throw new Error('未找到完成按钮');

                await simulateClick(doneButton);
                await sleep(10);

                return true;
            }
        } catch (error) {
            console.error('转换公式时出错:', error);
            updateStatus(`错误: ${error.message}`);
            throw error;
        }
    }

    // 优化的主转换函数
    async function convertFormulas() {
        if (isProcessing) return;
        isProcessing = true;
        convertBtn.classList.add('processing');

        try {
            formulaCount = 0;
            updateStatus('开始扫描文档...');

            const editors = document.querySelectorAll('[contenteditable="true"]');
            console.log('找到编辑区域数量:', editors.length);

            // 预先收集所有公式
            const allFormulas = [];
            let totalFormulas = 0;
            for (const editor of editors) {
                const text = editor.textContent;
                const formulas = findFormulas(text);
                totalFormulas += formulas.length;
                allFormulas.push({ editor, formulas });
            }

            if (totalFormulas === 0) {
                updateStatus('未找到需要转换的公式', 3000);
                updateProgress(0, 0);
                convertBtn.classList.remove('processing');
                isProcessing = false;
                return;
            }

            updateStatus(`找到 ${totalFormulas} 个公式，开始转换...`);

            // 从末尾开始处理公式
            for (const { editor, formulas } of allFormulas.reverse()) {
                for (const formulaObj of formulas.reverse()) {
                    await convertFormula(editor, formulaObj.formula, formulaObj.isMultiline);
                    formulaCount++;
                    updateProgress(formulaCount, totalFormulas);
                    updateStatus(`正在转换... (${formulaCount}/${totalFormulas})`);
                }
            }

            updateStatus(`Done:${formulaCount}`, 3000);
            convertBtn.textContent = `🔄 (${formulaCount})`;

        } catch (error) {
            console.error('转换过程出错:', error);
            updateStatus(`发生错误: ${error.message}`, 5000);
            updateProgress(0, 0);
        } finally {
            isProcessing = false;
            convertBtn.classList.remove('processing');

            setTimeout(() => {
                if (!isProcessing) {
                    updateProgress(0, 0);
                }
            }, 1000);
        }
    }

    // 点击事件模拟
    async function simulateClick(element) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const events = [
            new MouseEvent('mousemove', { bubbles: true, clientX: centerX, clientY: centerY }),
            new MouseEvent('mouseenter', { bubbles: true, clientX: centerX, clientY: centerY }),
            new MouseEvent('mousedown', { bubbles: true, clientX: centerX, clientY: centerY }),
            new MouseEvent('mouseup', { bubbles: true, clientX: centerX, clientY: centerY }),
            new MouseEvent('click', { bubbles: true, clientX: centerX, clientY: centerY })
        ];

        for (const event of events) {
            element.dispatchEvent(event);
            await sleep(20);
        }
    }

    // 初始化
    createPanel();
    convertBtn.addEventListener('click', convertFormulas);

    // 页面加载完成后检查公式数量
    setTimeout(() => {
        const formulas = findFormulas(document.body.textContent);
        if (formulas.length > 0) {
            convertBtn.textContent = `🔄(${formulas.length})`;
        }
    }, 1000);

    console.log('公式转换工具已加载');
})();
